const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"api.auth.login":{"uri":"api\/auth\/login","methods":["POST"]},"api.auth.login.google":{"uri":"api\/auth\/login\/google","methods":["POST"]},"api.auth.register":{"uri":"api\/auth\/register","methods":["POST"]},"api.auth.forgot-password":{"uri":"api\/auth\/forgot-password","methods":["POST"]},"api.auth.reset-password":{"uri":"api\/auth\/reset-password","methods":["POST"]},"api.campuses":{"uri":"api\/campuses","methods":["GET","HEAD"]},"api.admin.campuses.store":{"uri":"api\/campuses","methods":["POST"]},"api.admin.campuses.show":{"uri":"api\/campuses\/{campus}","methods":["GET","HEAD"],"parameters":["campus"],"bindings":{"campus":"id"}},"api.admin.campuses.update":{"uri":"api\/campuses\/{campus}","methods":["PUT","PATCH"],"parameters":["campus"],"bindings":{"campus":"id"}},"api.admin.campuses.destroy":{"uri":"api\/campuses\/{campus}","methods":["DELETE"],"parameters":["campus"],"bindings":{"campus":"id"}},"api.admin.buildings.index":{"uri":"api\/buildings","methods":["GET","HEAD"]},"api.admin.buildings.store":{"uri":"api\/buildings","methods":["POST"]},"api.admin.buildings.show":{"uri":"api\/buildings\/{building}","methods":["GET","HEAD"],"parameters":["building"],"bindings":{"building":"id"}},"api.admin.buildings.update":{"uri":"api\/buildings\/{building}","methods":["PUT","PATCH"],"parameters":["building"],"bindings":{"building":"id"}},"api.admin.buildings.destroy":{"uri":"api\/buildings\/{building}","methods":["DELETE"],"parameters":["building"],"bindings":{"building":"id"}},"api.admin.users.index":{"uri":"api\/users","methods":["GET","HEAD"]},"api.admin.users.store":{"uri":"api\/users","methods":["POST"]},"api.admin.users.show":{"uri":"api\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"api.admin.users.update":{"uri":"api\/users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"api.admin.users.destroy":{"uri":"api\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"api.admin.roles.index":{"uri":"api\/roles","methods":["GET","HEAD"]},"api.admin.roles.store":{"uri":"api\/roles","methods":["POST"]},"api.admin.roles.show":{"uri":"api\/roles\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"api.admin.roles.update":{"uri":"api\/roles\/{role}","methods":["PUT","PATCH"],"parameters":["role"],"bindings":{"role":"id"}},"api.admin.roles.destroy":{"uri":"api\/roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"api.admin.units.index":{"uri":"api\/units","methods":["GET","HEAD"]},"api.admin.units.store":{"uri":"api\/units","methods":["POST"]},"api.admin.units.show":{"uri":"api\/units\/{unit}","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.admin.units.update":{"uri":"api\/units\/{unit}","methods":["PUT","PATCH"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.admin.units.destroy":{"uri":"api\/units\/{unit}","methods":["DELETE"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.admin.programs.index":{"uri":"api\/programs","methods":["GET","HEAD"]},"api.admin.programs.store":{"uri":"api\/programs","methods":["POST"]},"api.admin.programs.show":{"uri":"api\/programs\/{program}","methods":["GET","HEAD"],"parameters":["program"],"bindings":{"program":"id"}},"api.admin.programs.update":{"uri":"api\/programs\/{program}","methods":["PUT","PATCH"],"parameters":["program"],"bindings":{"program":"id"}},"api.admin.programs.destroy":{"uri":"api\/programs\/{program}","methods":["DELETE"],"parameters":["program"],"bindings":{"program":"id"}},"api.admin.syllabus.index":{"uri":"api\/units\/{unit}\/syllabus","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.admin.syllabus.active":{"uri":"api\/units\/{unit}\/syllabus\/active","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.admin.syllabus.show":{"uri":"api\/units\/{unit}\/syllabus\/{syllabus}","methods":["GET","HEAD"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"api.admin.students.search":{"uri":"api\/students\/search","methods":["GET","HEAD"]},"api.admin.students.by-ids":{"uri":"api\/students\/by-ids","methods":["POST"]},"api.admin.students.index":{"uri":"api\/students","methods":["GET","HEAD"]},"api.admin.students.store":{"uri":"api\/students","methods":["POST"]},"api.admin.students.stats":{"uri":"api\/students\/stats","methods":["GET","HEAD"]},"api.admin.students.show":{"uri":"api\/students\/{student}","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"api.admin.students.update":{"uri":"api\/students\/{student}","methods":["PUT"],"parameters":["student"],"bindings":{"student":"id"}},"api.admin.students.patch":{"uri":"api\/students\/{student}","methods":["PATCH"],"parameters":["student"],"bindings":{"student":"id"}},"api.admin.students.destroy":{"uri":"api\/students\/{student}","methods":["DELETE"],"parameters":["student"],"bindings":{"student":"id"}},"api.registrations.index":{"uri":"api\/registrations","methods":["GET","HEAD"]},"api.admin.registrations.override":{"uri":"api\/registrations\/{registration}\/override","methods":["POST"],"parameters":["registration"]},"api.admin.registrations.reports":{"uri":"api\/registrations\/reports","methods":["GET","HEAD"]},"api.admin.class-sessions.index":{"uri":"api\/class-sessions","methods":["GET","HEAD"]},"api.admin.class-sessions.store":{"uri":"api\/class-sessions","methods":["POST"]},"api.admin.class-sessions.show":{"uri":"api\/class-sessions\/{class_session}","methods":["GET","HEAD"],"parameters":["class_session"]},"api.admin.class-sessions.update":{"uri":"api\/class-sessions\/{class_session}","methods":["PUT","PATCH"],"parameters":["class_session"]},"api.admin.class-sessions.destroy":{"uri":"api\/class-sessions\/{class_session}","methods":["DELETE"],"parameters":["class_session"]},"api.admin.class-sessions.attendances":{"uri":"api\/class-sessions\/{classSession}\/attendances","methods":["GET","HEAD"],"parameters":["classSession"]},"api.admin.class-sessions.start":{"uri":"api\/class-sessions\/{classSession}\/start","methods":["POST"],"parameters":["classSession"]},"api.admin.class-sessions.complete":{"uri":"api\/class-sessions\/{classSession}\/complete","methods":["POST"],"parameters":["classSession"]},"api.admin.class-sessions.cancel":{"uri":"api\/class-sessions\/{classSession}\/cancel","methods":["POST"],"parameters":["classSession"]},"api.admin.class-sessions.generate-attendance":{"uri":"api\/class-sessions\/{classSession}\/generate-attendance","methods":["POST"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"api.health":{"uri":"api\/health","methods":["GET","HEAD"]},"api.auth.logout":{"uri":"api\/auth\/logout","methods":["POST"]},"api.auth.me":{"uri":"api\/auth\/me","methods":["GET","HEAD"]},"api.auth.refresh":{"uri":"api\/auth\/refresh","methods":["POST"]},"api.student.profile":{"uri":"api\/student\/profile","methods":["GET","HEAD"]},"api.student.update-profile":{"uri":"api\/student\/profile","methods":["PUT"]},"api.student.academic-record":{"uri":"api\/student\/academic-record","methods":["GET","HEAD"]},"api.student.holds":{"uri":"api\/student\/holds","methods":["GET","HEAD"]},"api.student.graduation-progress":{"uri":"api\/student\/graduation-progress","methods":["GET","HEAD"]},"api.courses.available":{"uri":"api\/courses\/available","methods":["GET","HEAD"]},"api.courses.show":{"uri":"api\/courses\/{courseOffering}","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"api.courses.prerequisites":{"uri":"api\/courses\/{courseOffering}\/prerequisites","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"api.semesters.index":{"uri":"api\/semesters","methods":["GET","HEAD"]},"api.semesters.courses":{"uri":"api\/semesters\/{semester}\/courses","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.registrations.current":{"uri":"api\/registrations\/current","methods":["GET","HEAD"]},"api.registrations.store":{"uri":"api\/registrations","methods":["POST"]},"api.registrations.drop":{"uri":"api\/registrations\/{registration}","methods":["DELETE"],"parameters":["registration"],"bindings":{"registration":"id"}},"api.registrations.withdraw":{"uri":"api\/registrations\/{registration}\/withdraw","methods":["POST"],"parameters":["registration"],"bindings":{"registration":"id"}},"api.registrations.eligibility":{"uri":"api\/registrations\/eligibility\/{courseOffering}","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"api.registrations.schedule-conflicts":{"uri":"api\/registrations\/schedule-conflicts\/{courseOffering}","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"api.academic.transcript":{"uri":"api\/academic\/transcript","methods":["GET","HEAD"]},"api.academic.gpa":{"uri":"api\/academic\/gpa","methods":["GET","HEAD"]},"api.academic.credit-summary":{"uri":"api\/academic\/credit-summary","methods":["GET","HEAD"]},"api.lecturer.auth.logout":{"uri":"api\/auth\/lecturer\/logout","methods":["POST"]},"api.lecturer.auth.me":{"uri":"api\/auth\/lecturer\/me","methods":["GET","HEAD"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"select-campus.index":{"uri":"select-campus","methods":["GET","HEAD"]},"select-campus.set-current":{"uri":"select-campus\/set-current","methods":["POST"]},"curriculum_versions.index":{"uri":"curriculum-versions","methods":["GET","HEAD"]},"curriculum_versions.create":{"uri":"curriculum-versions\/create","methods":["GET","HEAD"]},"curriculum_versions.store":{"uri":"curriculum-versions","methods":["POST"]},"curriculum_versions.show":{"uri":"curriculum-versions\/{curriculum_version}","methods":["GET","HEAD"],"parameters":["curriculum_version"]},"curriculum_versions.edit":{"uri":"curriculum-versions\/{curriculum_version}\/edit","methods":["GET","HEAD"],"parameters":["curriculum_version"]},"curriculum-versions.update":{"uri":"curriculum-versions\/{curriculum_version}","methods":["PUT","PATCH"],"parameters":["curriculum_version"]},"curriculum_versions.destroy":{"uri":"curriculum-versions\/{curriculum_version}","methods":["DELETE"],"parameters":["curriculum_version"]},"curriculum_version.electives":{"uri":"curriculum-versions\/{curriculumVersion}\/electives","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.available-electives":{"uri":"api\/curriculum-versions\/{curriculumVersion}\/available-electives","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.elective-slots":{"uri":"api\/curriculum-versions\/{curriculumVersion}\/elective-slots","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum-units.update-elective":{"uri":"api\/curriculum-units\/{curriculumUnit}\/update-elective","methods":["PUT"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.units.details":{"uri":"api\/units\/{unit}\/details","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.curriculum-units.recommendations":{"uri":"api\/curriculum-units\/{curriculumUnit}\/recommendations","methods":["GET","HEAD"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"google.login":{"uri":"auth\/google\/redirect","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"users.import.form":{"uri":"users\/import","methods":["GET","HEAD"]},"users.import.upload":{"uri":"users\/import\/upload","methods":["POST"]},"users.import.preview":{"uri":"users\/import\/preview","methods":["POST"]},"users.import.process":{"uri":"users\/import\/process","methods":["POST"]},"users.import.history":{"uri":"users\/import\/history","methods":["GET","HEAD"]},"users.import.debug":{"uri":"users\/import\/debug","methods":["GET","HEAD"]},"users.templates.download":{"uri":"users\/templates\/{format}","methods":["GET","HEAD"],"wheres":{"format":"simple|detailed|relationship"},"parameters":["format"]},"users.export.excel":{"uri":"users\/export\/excel","methods":["GET","HEAD"]},"users.export.excel.filtered":{"uri":"users\/export\/excel\/filtered","methods":["GET","HEAD"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"users.update":{"uri":"users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"users.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"roles.index":{"uri":"roles","methods":["GET","HEAD"]},"roles.create":{"uri":"roles\/create","methods":["GET","HEAD"]},"roles.store":{"uri":"roles","methods":["POST"]},"roles.show":{"uri":"roles\/{role}","methods":["GET","HEAD"],"parameters":["role"]},"roles.edit":{"uri":"roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"roles.update":{"uri":"roles\/{role}","methods":["PUT"],"parameters":["role"],"bindings":{"role":"id"}},"roles.destroy":{"uri":"roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"semesters.index":{"uri":"semesters","methods":["GET","HEAD"]},"semesters.create":{"uri":"semesters\/create","methods":["GET","HEAD"]},"semesters.store":{"uri":"semesters","methods":["POST"]},"semesters.show":{"uri":"semesters\/{semester}","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.edit":{"uri":"semesters\/{semester}\/edit","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.update":{"uri":"semesters\/{semester}","methods":["PUT"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.destroy":{"uri":"semesters\/{semester}","methods":["DELETE"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.activate":{"uri":"semesters\/{semester}\/activate","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.deactivate":{"uri":"semesters\/{semester}\/deactivate","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.activation-statuses":{"uri":"semesters\/activation-statuses","methods":["GET","HEAD"]},"semesters.enrollment.show":{"uri":"semesters\/{semester}\/enrollment","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.generate":{"uri":"api\/semesters\/{semester}\/enrollment\/generate","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.suggested-courses":{"uri":"api\/semesters\/{semester}\/enrollment\/suggested-courses","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.bulk-open-courses":{"uri":"api\/semesters\/{semester}\/enrollment\/bulk-open-courses","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.open-single-course":{"uri":"api\/semesters\/{semester}\/enrollment\/open-single-course","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.stats":{"uri":"api\/semesters\/{semester}\/enrollment\/stats","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.registrable-students":{"uri":"api\/semesters\/{semester}\/enrollment\/registrable-students","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"api.semesters.enrollment.bulk-register":{"uri":"api\/semesters\/{semester}\/enrollment\/bulk-register","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"units.export.excel":{"uri":"units\/export\/excel","methods":["GET","HEAD"]},"units.export.excel.filtered":{"uri":"units\/export\/excel\/filtered","methods":["GET","HEAD"]},"units.import":{"uri":"units\/import","methods":["GET","HEAD"]},"units.import.upload":{"uri":"units\/import\/upload","methods":["POST"]},"units.import.preview":{"uri":"units\/import\/preview","methods":["POST"]},"units.import.process":{"uri":"units\/import\/process","methods":["POST"]},"units.import.template":{"uri":"units\/import\/template\/{format}","methods":["GET","HEAD"],"parameters":["format"]},"units.import.history":{"uri":"units\/import\/history","methods":["GET","HEAD"]},"units.index":{"uri":"units","methods":["GET","HEAD"]},"units.create":{"uri":"units\/create","methods":["GET","HEAD"]},"units.store":{"uri":"units","methods":["POST"]},"units.show":{"uri":"units\/{unit}","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"units.edit":{"uri":"units\/{unit}\/edit","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"units.update":{"uri":"units\/{unit}","methods":["PUT"],"parameters":["unit"],"bindings":{"unit":"id"}},"units.destroy":{"uri":"units\/{unit}","methods":["DELETE"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.units.search":{"uri":"api\/units\/search","methods":["GET","HEAD"]},"api.units.validate-code":{"uri":"api\/units\/validate-code","methods":["POST"]},"api.units.validate-prerequisite-expression":{"uri":"api\/units\/validate-prerequisite-expression","methods":["POST"]},"api.units.bulk-delete":{"uri":"api\/units\/bulk-delete","methods":["DELETE"]},"syllabus.index":{"uri":"units\/{unit}\/syllabus","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.create":{"uri":"units\/{unit}\/syllabus\/create","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.store":{"uri":"units\/{unit}\/syllabus","methods":["POST"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.show":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["GET","HEAD"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.edit":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/edit","methods":["GET","HEAD"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.update":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["PUT"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.destroy":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["DELETE"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.toggle-active":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/toggle-active","methods":["PATCH"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.clone":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/clone","methods":["POST"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"programs.index":{"uri":"programs","methods":["GET","HEAD"]},"programs.store":{"uri":"programs","methods":["POST"]},"programs.show":{"uri":"programs\/{program}","methods":["GET","HEAD"],"parameters":["program"],"bindings":{"program":"id"}},"programs.update":{"uri":"programs\/{program}","methods":["PUT"],"parameters":["program"],"bindings":{"program":"id"}},"programs.destroy":{"uri":"programs\/{program}","methods":["DELETE"],"parameters":["program"],"bindings":{"program":"id"}},"api.programs.search":{"uri":"api\/programs\/search","methods":["GET","HEAD"]},"api.programs.bulk-delete":{"uri":"api\/programs\/bulk-delete","methods":["DELETE"]},"specializations.index":{"uri":"specializations","methods":["GET","HEAD"]},"specializations.create":{"uri":"specializations\/create","methods":["GET","HEAD"]},"specializations.edit":{"uri":"specializations\/{specialization}\/edit","methods":["GET","HEAD"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.store":{"uri":"specializations","methods":["POST"]},"specializations.show":{"uri":"specializations\/{specialization}","methods":["GET","HEAD"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.update":{"uri":"specializations\/{specialization}","methods":["PUT"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.destroy":{"uri":"specializations\/{specialization}","methods":["DELETE"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"api.specializations.destroy":{"uri":"api\/specializations\/{specialization}","methods":["DELETE"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"api.specializations.bulk-delete":{"uri":"api\/specializations\/bulk-delete","methods":["DELETE"]},"api.curriculum_versions.destroy":{"uri":"api\/curriculum-versions\/{curriculumVersion}","methods":["DELETE"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.update":{"uri":"api\/curriculum-versions\/{curriculumVersion}","methods":["PUT"],"parameters":["curriculumVersion"]},"curriculum_versions.update":{"uri":"curriculum-versions\/{curriculum_version}","methods":["PUT"],"parameters":["curriculum_version"]},"curriculum_versions.export.filtered":{"uri":"curriculum-versions\/export\/excel\/filtered","methods":["GET","HEAD"]},"curriculum_unit.index":{"uri":"curriculum-units","methods":["GET","HEAD"]},"curriculum_unit.create":{"uri":"curriculum-units\/create","methods":["GET","HEAD"]},"curriculum_unit.store":{"uri":"curriculum-units","methods":["POST"]},"curriculum_unit.show":{"uri":"curriculum-units\/{curriculum_unit}","methods":["GET","HEAD"],"parameters":["curriculum_unit"]},"curriculum_unit.edit":{"uri":"curriculum-units\/{curriculum_unit}\/edit","methods":["GET","HEAD"],"parameters":["curriculum_unit"]},"curriculum_unit.update":{"uri":"curriculum-units\/{curriculum_unit}","methods":["PUT"],"parameters":["curriculum_unit"]},"curriculum_unit.destroy":{"uri":"curriculum-units\/{curriculum_unit}","methods":["DELETE"],"parameters":["curriculum_unit"]},"api.curriculum_versions.store":{"uri":"api\/curriculum-versions","methods":["POST"]},"api.curriculum_versions.specializations-by-program":{"uri":"api\/curriculum-versions\/specializations-by-program","methods":["GET","HEAD"]},"api.curriculum_versions.by-program-specialization":{"uri":"api\/curriculum-versions\/by-program-specialization","methods":["GET","HEAD"]},"api.curriculum_versions.bulk-delete":{"uri":"api\/curriculum-versions\/bulk-delete","methods":["DELETE"]},"api.curriculum_versions.bulk-operations":{"uri":"api\/curriculum-versions\/bulk-operations","methods":["POST"]},"api.curriculum-units.store":{"uri":"api\/curriculum-units","methods":["POST"]},"api.curriculum-units.update":{"uri":"api\/curriculum-units\/{curriculumUnit}","methods":["PUT"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.curriculum-units.destroy":{"uri":"api\/curriculum-units\/{curriculumUnit}","methods":["DELETE"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.curriculum-units.by-curriculum-version":{"uri":"api\/curriculum-units\/by-curriculum-version","methods":["GET","HEAD"]},"api.curriculum-units.bulk-delete":{"uri":"api\/curriculum-units\/bulk-delete","methods":["DELETE"]},"course-offerings.index":{"uri":"course-offerings","methods":["GET","HEAD"]},"course-offerings.create":{"uri":"course-offerings\/create","methods":["GET","HEAD"]},"course-offerings.edit":{"uri":"course-offerings\/{courseOffering}\/edit","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.store":{"uri":"course-offerings","methods":["POST"]},"course-offerings.show":{"uri":"course-offerings\/{courseOffering}","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.update":{"uri":"course-offerings\/{courseOffering}","methods":["PUT"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.destroy":{"uri":"course-offerings\/{courseOffering}","methods":["DELETE"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.toggle-status":{"uri":"course-offerings\/{courseOffering}\/toggle-status","methods":["PATCH"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.split.show":{"uri":"course-offerings\/{courseOffering}\/split","methods":["GET","HEAD"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-offerings.split.perform":{"uri":"course-offerings\/{courseOffering}\/split","methods":["POST"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"api.course-offerings.bulk-delete":{"uri":"api\/course-offerings\/bulk-delete","methods":["DELETE"]},"api.course-offerings.statistics":{"uri":"api\/course-offerings\/statistics","methods":["GET","HEAD"]},"api.course-offerings.check-instructor-assignments":{"uri":"api\/course-offerings\/check-instructor-assignments","methods":["GET","HEAD"]},"api.course-offerings.bulk-assign-lectures":{"uri":"api\/course-offerings\/bulk-assign-lectures","methods":["POST"]},"api.course-offerings.bulk-update-status":{"uri":"api\/course-offerings\/{courseOffering}\/bulk-update-status","methods":["POST"],"parameters":["courseOffering"],"bindings":{"courseOffering":"id"}},"course-registrations.index":{"uri":"course-registrations","methods":["GET","HEAD"]},"course-registrations.create":{"uri":"course-registrations\/create","methods":["GET","HEAD"]},"course-registrations.store":{"uri":"course-registrations","methods":["POST"]},"course-registrations.show":{"uri":"course-registrations\/{adminCourseRegistration}","methods":["GET","HEAD"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"course-registrations.edit":{"uri":"course-registrations\/{adminCourseRegistration}\/edit","methods":["GET","HEAD"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"course-registrations.update":{"uri":"course-registrations\/{adminCourseRegistration}","methods":["PUT"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"course-registrations.destroy":{"uri":"course-registrations\/{adminCourseRegistration}","methods":["DELETE"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"course-registrations.drop":{"uri":"course-registrations\/{adminCourseRegistration}\/drop","methods":["PATCH"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"course-registrations.withdraw":{"uri":"course-registrations\/{adminCourseRegistration}\/withdraw","methods":["PATCH"],"parameters":["adminCourseRegistration"],"bindings":{"adminCourseRegistration":"id"}},"api.course-registrations.bulk-delete":{"uri":"api\/course-registrations\/bulk-delete","methods":["DELETE"]},"api.course-registrations.available-courses":{"uri":"api\/course-registrations\/available-courses","methods":["GET","HEAD"]},"api.course-registrations.check-eligibility":{"uri":"api\/course-registrations\/check-eligibility","methods":["GET","HEAD"]},"api.course-registrations.student-registrations":{"uri":"api\/course-registrations\/student-registrations","methods":["GET","HEAD"]},"campuses.index":{"uri":"campuses","methods":["GET","HEAD"]},"campuses.create":{"uri":"campuses\/create","methods":["GET","HEAD"]},"campuses.store":{"uri":"campuses","methods":["POST"]},"campuses.show":{"uri":"campuses\/{campus}","methods":["GET","HEAD"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.edit":{"uri":"campuses\/{campus}\/edit","methods":["GET","HEAD"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.update":{"uri":"campuses\/{campus}","methods":["PUT"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.destroy":{"uri":"campuses\/{campus}","methods":["DELETE"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.buildings.create":{"uri":"campuses\/{campus}\/buildings\/create","methods":["GET","HEAD"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.buildings.store":{"uri":"campuses\/{campus}\/buildings","methods":["POST"],"parameters":["campus"],"bindings":{"campus":"id"}},"campuses.buildings.edit":{"uri":"campuses\/{campus}\/buildings\/{building}\/edit","methods":["GET","HEAD"],"parameters":["campus","building"],"bindings":{"campus":"id","building":"id"}},"campuses.buildings.update":{"uri":"campuses\/{campus}\/buildings\/{building}","methods":["PUT"],"parameters":["campus","building"],"bindings":{"campus":"id","building":"id"}},"campuses.buildings.destroy":{"uri":"campuses\/{campus}\/buildings\/{building}","methods":["DELETE"],"parameters":["campus","building"],"bindings":{"campus":"id","building":"id"}},"students.index":{"uri":"students","methods":["GET","HEAD"]},"students.new-students.index":{"uri":"students\/new-students","methods":["GET","HEAD"]},"students.new-students.bulk-onboarding":{"uri":"students\/new-students\/bulk-onboarding","methods":["POST"]},"students.create":{"uri":"students\/create","methods":["GET","HEAD"]},"students.store":{"uri":"students","methods":["POST"]},"students.show":{"uri":"students\/{student}","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.edit":{"uri":"students\/{student}\/edit","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.update":{"uri":"students\/{student}","methods":["PUT"],"parameters":["student"],"bindings":{"student":"id"}},"students.destroy":{"uri":"students\/{student}","methods":["DELETE"],"parameters":["student"],"bindings":{"student":"id"}},"students.assign-program":{"uri":"students\/{student}\/assign-program","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"students.update-status":{"uri":"students\/{student}\/update-status","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.show":{"uri":"students\/{student}\/academic-summary","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.filter-by-semester":{"uri":"students\/{student}\/academic-summary\/filter-by-semester","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.filter-by-course-offering":{"uri":"students\/{student}\/academic-summary\/filter-by-course-offering","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.attendance-details":{"uri":"students\/{student}\/academic-summary\/attendance-details","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.score-details":{"uri":"students\/{student}\/academic-summary\/score-details","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-summary.course-scores":{"uri":"students\/{student}\/academic-summary\/course-scores\/{courseOfferingId}","methods":["GET","HEAD"],"parameters":["student","courseOfferingId"],"bindings":{"student":"id"}},"academic-records.index":{"uri":"academic-records","methods":["GET","HEAD"]},"academic-records.analytics":{"uri":"academic-records\/analytics","methods":["GET","HEAD"]},"students.academic-records.index":{"uri":"students\/{student}\/academic-records","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-records.show":{"uri":"students\/{student}\/academic-records\/{record}","methods":["GET","HEAD"],"parameters":["student","record"],"bindings":{"student":"id"}},"students.academic-records.store":{"uri":"students\/{student}\/academic-records","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-records.transcript":{"uri":"students\/{student}\/academic-records\/transcript\/view","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.academic-records.gpa-history":{"uri":"students\/{student}\/academic-records\/gpa\/history","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"program-changes.index":{"uri":"program-changes","methods":["GET","HEAD"]},"program-changes.show":{"uri":"program-changes\/{programChangeRequest}","methods":["GET","HEAD"],"parameters":["programChangeRequest"],"bindings":{"programChangeRequest":"id"}},"program-changes.approve":{"uri":"program-changes\/{programChangeRequest}\/approve","methods":["PATCH"],"parameters":["programChangeRequest"],"bindings":{"programChangeRequest":"id"}},"program-changes.reject":{"uri":"program-changes\/{programChangeRequest}\/reject","methods":["PATCH"],"parameters":["programChangeRequest"],"bindings":{"programChangeRequest":"id"}},"students.program-changes.create":{"uri":"students\/{student}\/program-changes\/create","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.program-changes.store":{"uri":"students\/{student}\/program-changes","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"students.program-changes.show":{"uri":"students\/{student}\/program-changes\/{programChangeRequest}","methods":["GET","HEAD"],"parameters":["student","programChangeRequest"],"bindings":{"programChangeRequest":"id"}},"students.program-changes.evaluate-credits":{"uri":"students\/{student}\/program-changes\/evaluate-credits","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"course-retakes.index":{"uri":"course-retakes","methods":["GET","HEAD"]},"course-retakes.statistics":{"uri":"course-retakes\/statistics","methods":["GET","HEAD"]},"students.retakes.index":{"uri":"students\/{student}\/retakes","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.retakes.create":{"uri":"students\/{student}\/retakes\/create","methods":["GET","HEAD"],"parameters":["student"]},"students.retakes.store":{"uri":"students\/{student}\/retakes","methods":["POST"],"parameters":["student"]},"students.retakes.show":{"uri":"students\/{student}\/retakes\/{registration}","methods":["GET","HEAD"],"parameters":["student","registration"]},"students.standing.index":{"uri":"students\/{student}\/standing","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.standing.create":{"uri":"students\/{student}\/standing\/create","methods":["GET","HEAD"],"parameters":["student"]},"students.standing.store":{"uri":"students\/{student}\/standing","methods":["POST"],"parameters":["student"]},"students.standing.show":{"uri":"students\/{student}\/standing\/{standing}","methods":["GET","HEAD"],"parameters":["student","standing"]},"academic-standings.index":{"uri":"academic-standings","methods":["GET","HEAD"]},"academic-standings.bulk-update":{"uri":"academic-standings\/bulk-update","methods":["POST"]},"student-enrollments.index":{"uri":"student-enrollments","methods":["GET","HEAD"]},"student-enrollments.holds":{"uri":"student-enrollments\/holds","methods":["GET","HEAD"]},"students.status.index":{"uri":"students\/status-tracking","methods":["GET","HEAD"]},"students.status.statistics":{"uri":"students\/status-tracking\/statistics","methods":["GET","HEAD"]},"students.status.show":{"uri":"students\/{student}\/status","methods":["GET","HEAD"],"parameters":["student"]},"students.status.update":{"uri":"students\/{student}\/status\/update","methods":["PATCH"],"parameters":["student"]},"students.status.history":{"uri":"students\/{student}\/status\/history","methods":["GET","HEAD"],"parameters":["student"]},"units.grade-distribution":{"uri":"units\/{unit}\/grade-distribution","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"class-sessions.index":{"uri":"class-sessions","methods":["GET","HEAD"]},"class-sessions.create":{"uri":"class-sessions\/create","methods":["GET","HEAD"]},"class-sessions.edit":{"uri":"class-sessions\/{classSession}\/edit","methods":["GET","HEAD"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"class-sessions.store":{"uri":"class-sessions","methods":["POST"]},"class-sessions.show":{"uri":"class-sessions\/{classSession}","methods":["GET","HEAD"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"class-sessions.update":{"uri":"class-sessions\/{classSession}","methods":["PUT"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"class-sessions.destroy":{"uri":"class-sessions\/{classSession}","methods":["DELETE"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"class-sessions.generate-attendance":{"uri":"class-sessions\/{classSession}\/generate-attendance","methods":["POST"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"class-sessions.export-attendance":{"uri":"class-sessions\/{classSession}\/export-attendance","methods":["GET","HEAD"],"parameters":["classSession"],"bindings":{"classSession":"id"}},"attendance.index":{"uri":"attendance","methods":["GET","HEAD"]},"attendance.create":{"uri":"attendance\/create","methods":["GET","HEAD"]},"attendance.show":{"uri":"attendance\/{attendance}","methods":["GET","HEAD"],"parameters":["attendance"],"bindings":{"attendance":"id"}},"attendance.edit":{"uri":"attendance\/{attendance}\/edit","methods":["GET","HEAD"],"parameters":["attendance"],"bindings":{"attendance":"id"}},"attendance.store":{"uri":"attendance","methods":["POST"]},"attendance.update":{"uri":"attendance\/{attendance}","methods":["PUT"],"parameters":["attendance"],"bindings":{"attendance":"id"}},"attendance.destroy":{"uri":"attendance\/{attendance}","methods":["DELETE"],"parameters":["attendance"],"bindings":{"attendance":"id"}},"attendance.bulk-update":{"uri":"attendance\/bulk-update","methods":["POST"]},"attendance.api.bulk-update":{"uri":"api\/attendance\/bulk-update","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
