{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "Bash(php artisan make:migration:*)", "Bash(php artisan make:model:*)", "Bash(php artisan make:service:*)", "Bash(php artisan make:controller:*)", "Bash(php artisan:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(php -l:*)", "Bash(./vendor/bin/pint:*)", "Bash(find:*)", "Bash(grep:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "mcp__serena__find_symbol", "mcp__serena__insert_after_symbol", "mcp__serena__insert_before_symbol", "Bash(npm run build:*)"], "deny": []}}