<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseOfferingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource['id'],
            'unit' => [
                'code' => $this->resource['unit']['code'],
                'name' => $this->resource['unit']['name'],
                'description' => $this->resource['unit']['description'],
                'credit_hours' => $this->resource['unit']['credit_hours'],
            ],
            'lecturer' => [
                'id' => $this->resource['lecturer']['id'],
                'name' => $this->resource['lecturer']['name'],
                'email' => $this->resource['lecturer']['email'],
            ],
            'schedule' => $this->formatSchedule($this->resource['schedule']),
            'enrollment' => [
                'current' => $this->resource['enrollment']['current'],
                'maximum' => $this->resource['enrollment']['maximum'],
                'available' => $this->resource['enrollment']['available'],
                'waitlist_available' => $this->resource['enrollment']['waitlist_available'],
                'enrollment_status' => $this->getEnrollmentStatus($this->resource['enrollment']),
            ],
            'registration_eligibility' => [
                'can_register' => $this->resource['registration_eligibility']['can_register'],
                'prerequisites_met' => $this->resource['registration_eligibility']['prerequisites_met'],
                'has_conflicts' => $this->resource['registration_eligibility']['has_conflicts'],
                'capacity_available' => $this->resource['registration_eligibility']['capacity_available'],
                'eligibility_summary' => $this->getEligibilitySummary($this->resource['registration_eligibility']),
            ],
        ];
    }

    /**
     * Format schedule data for display
     */
    protected function formatSchedule(array $schedule): array
    {
        return collect($schedule)->map(function ($session) {
            return [
                'day_of_week' => $session['day_of_week'],
                'day_display' => ucfirst($session['day_of_week']),
                'start_time' => $session['start_time'],
                'end_time' => $session['end_time'],
                'duration' => $this->calculateDuration($session['start_time'], $session['end_time']),
                'room' => [
                    'code' => $session['room']['code'],
                    'name' => $session['room']['name'],
                    'building' => $session['room']['building'],
                    'full_location' => $this->formatRoomLocation($session['room']),
                ],
            ];
        })->toArray();
    }

    /**
     * Get enrollment status description
     */
    protected function getEnrollmentStatus(array $enrollment): string
    {
        $percentage = $enrollment['maximum'] > 0 
            ? ($enrollment['current'] / $enrollment['maximum']) * 100 
            : 0;

        return match (true) {
            $enrollment['available'] === 0 => 'full',
            $percentage >= 90 => 'nearly_full',
            $percentage >= 75 => 'filling_up',
            $percentage >= 50 => 'half_full',
            $percentage >= 25 => 'available',
            default => 'open',
        };
    }

    /**
     * Get eligibility summary
     */
    protected function getEligibilitySummary(array $eligibility): string
    {
        if ($eligibility['can_register']) {
            return 'eligible';
        }

        $reasons = [];
        
        if (!$eligibility['prerequisites_met']) {
            $reasons[] = 'prerequisites not met';
        }
        
        if ($eligibility['has_conflicts']) {
            $reasons[] = 'schedule conflicts';
        }
        
        if (!$eligibility['capacity_available']) {
            $reasons[] = 'course full';
        }

        return 'not eligible: ' . implode(', ', $reasons);
    }

    /**
     * Calculate session duration in minutes
     */
    protected function calculateDuration(string $startTime, string $endTime): int
    {
        $start = \Carbon\Carbon::createFromTimeString($startTime);
        $end = \Carbon\Carbon::createFromTimeString($endTime);
        
        return $start->diffInMinutes($end);
    }

    /**
     * Format room location for display
     */
    protected function formatRoomLocation(array $room): string
    {
        $parts = array_filter([
            $room['code'],
            $room['name'],
            $room['building'],
        ]);

        return implode(' - ', $parts);
    }
}
