<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1\Student;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseRegistrationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource['id'],
            'status' => $this->resource['status'],
            'status_display' => $this->getStatusDisplay($this->resource['status']),
            'registration_date' => $this->resource['registration_date'],
            'drop_date' => $this->resource['drop_date'],
            'credit_hours' => $this->resource['credit_hours'],
            'unit' => [
                'code' => $this->resource['unit']['code'],
                'name' => $this->resource['unit']['name'],
            ],
            'lecturer' => [
                'name' => $this->resource['lecturer']['name'],
                'email' => $this->resource['lecturer']['email'],
            ],
            'schedule' => $this->formatSchedule($this->resource['schedule']),
            'semester' => [
                'name' => $this->resource['semester']['name'],
                'code' => $this->resource['semester']['code'],
            ],
            'actions' => $this->getAvailableActions($this->resource['status']),
        ];
    }

    /**
     * Get status display text
     */
    protected function getStatusDisplay(string $status): string
    {
        return match ($status) {
            'registered' => 'Registered',
            'pending' => 'Pending',
            'waitlisted' => 'Waitlisted',
            'dropped' => 'Dropped',
            'completed' => 'Completed',
            'failed' => 'Failed',
            default => ucfirst($status),
        };
    }

    /**
     * Format schedule for display
     */
    protected function formatSchedule(array $schedule): array
    {
        return collect($schedule)->map(function ($session) {
            return [
                'day_of_week' => $session['day_of_week'],
                'day_display' => ucfirst($session['day_of_week']),
                'start_time' => $session['start_time'],
                'end_time' => $session['end_time'],
                'time_display' => $this->formatTimeRange($session['start_time'], $session['end_time']),
                'room' => $session['room'],
            ];
        })->toArray();
    }

    /**
     * Format time range for display
     */
    protected function formatTimeRange(string $startTime, string $endTime): string
    {
        $start = \Carbon\Carbon::createFromTimeString($startTime);
        $end = \Carbon\Carbon::createFromTimeString($endTime);
        
        return $start->format('g:i A') . ' - ' . $end->format('g:i A');
    }

    /**
     * Get available actions for the registration
     */
    protected function getAvailableActions(string $status): array
    {
        $actions = [];

        switch ($status) {
            case 'registered':
                $actions[] = [
                    'action' => 'drop',
                    'label' => 'Drop Course',
                    'method' => 'DELETE',
                    'confirmation_required' => true,
                ];
                break;

            case 'waitlisted':
                $actions[] = [
                    'action' => 'remove_from_waitlist',
                    'label' => 'Remove from Waitlist',
                    'method' => 'DELETE',
                    'confirmation_required' => true,
                ];
                break;

            case 'pending':
                $actions[] = [
                    'action' => 'cancel',
                    'label' => 'Cancel Registration',
                    'method' => 'DELETE',
                    'confirmation_required' => true,
                ];
                break;
        }

        return $actions;
    }
}
