<?php

declare(strict_types=1);

namespace App\Services\V1\Student;

use App\Models\Student;
use App\Models\CourseOffering;
use App\Models\CourseRegistration;
use App\Models\Semester;
use App\Exceptions\BusinessLogicException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CourseRegistrationService
{
    public function __construct(
        protected ConflictDetectionService $conflictDetectionService,
        protected EnrollmentCapacityService $capacityService,
        protected PrerequisiteValidationService $prerequisiteService
    ) {}

    /**
     * Get available courses for registration
     */
    public function getAvailableCourses(Student $student, array $filters = []): Collection
    {
        $currentSemester = Semester::where('is_active', true)->first();
        
        if (!$currentSemester) {
            throw new BusinessLogicException('No active semester for registration');
        }

        $query = CourseOffering::where('semester_id', $currentSemester->id)
            ->where('is_active', true)
            ->with([
                'curriculumUnit.unit',
                'lecturer',
                'room',
                'classSessions.room',
                'courseRegistrations' => function ($q) {
                    $q->where('registration_status', 'registered');
                }
            ]);

        // Apply filters
        if (!empty($filters['unit_code'])) {
            $query->whereHas('curriculumUnit.unit', function ($q) use ($filters) {
                $q->where('code', 'like', '%' . $filters['unit_code'] . '%');
            });
        }

        if (!empty($filters['unit_name'])) {
            $query->whereHas('curriculumUnit.unit', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['unit_name'] . '%');
            });
        }

        if (!empty($filters['lecturer'])) {
            $query->whereHas('lecturer', function ($q) use ($filters) {
                $q->where('full_name', 'like', '%' . $filters['lecturer'] . '%');
            });
        }

        if (!empty($filters['day_of_week'])) {
            $query->whereHas('classSessions', function ($q) use ($filters) {
                $q->where('day_of_week', $filters['day_of_week']);
            });
        }

        if (!empty($filters['time_slot'])) {
            $query->whereHas('classSessions', function ($q) use ($filters) {
                $q->where('start_time', '>=', $filters['time_slot']['start'])
                  ->where('end_time', '<=', $filters['time_slot']['end']);
            });
        }

        $courseOfferings = $query->get();

        // Filter out courses already registered by student
        $registeredCourseIds = $student->courseRegistrations()
            ->where('semester_id', $currentSemester->id)
            ->whereIn('registration_status', ['registered', 'pending'])
            ->pluck('course_offering_id')
            ->toArray();

        return $courseOfferings->reject(function ($offering) use ($registeredCourseIds) {
            return in_array($offering->id, $registeredCourseIds);
        })->map(function ($offering) use ($student) {
            return $this->formatCourseOfferingForRegistration($offering, $student);
        });
    }

    /**
     * Register student for a course
     */
    public function registerForCourse(Student $student, int $courseOfferingId): CourseRegistration
    {
        $courseOffering = CourseOffering::with([
            'curriculumUnit.unit',
            'classSessions',
            'courseRegistrations'
        ])->findOrFail($courseOfferingId);

        // Validate registration
        $this->validateRegistration($student, $courseOffering);

        return DB::transaction(function () use ($student, $courseOffering) {
            // Create registration record
            $registration = CourseRegistration::create([
                'student_id' => $student->id,
                'course_offering_id' => $courseOffering->id,
                'semester_id' => $courseOffering->semester_id,
                'registration_status' => 'registered',
                'registration_date' => now(),
                'credit_hours' => $courseOffering->curriculumUnit->credit_hours,
                'registration_type' => 'regular',
            ]);

            // Update enrollment capacity
            $this->capacityService->updateEnrollmentCount($courseOffering);

            return $registration;
        });
    }

    /**
     * Drop a course registration
     */
    public function dropCourse(Student $student, CourseRegistration $registration): bool
    {
        if ($registration->student_id !== $student->id) {
            throw new BusinessLogicException('You can only drop your own registrations');
        }

        if (!in_array($registration->registration_status, ['registered', 'pending'])) {
            throw new BusinessLogicException('Cannot drop course with status: ' . $registration->registration_status);
        }

        // Check drop deadline
        $currentSemester = Semester::where('is_active', true)->first();
        if ($currentSemester && $currentSemester->drop_deadline && now()->isAfter($currentSemester->drop_deadline)) {
            throw new BusinessLogicException('Drop deadline has passed');
        }

        return DB::transaction(function () use ($registration) {
            $courseOffering = $registration->courseOffering;
            
            // Update registration status
            $registration->update([
                'registration_status' => 'dropped',
                'drop_date' => now(),
            ]);

            // Update enrollment capacity
            $this->capacityService->updateEnrollmentCount($courseOffering);

            return true;
        });
    }

    /**
     * Get student's current registrations
     */
    public function getStudentRegistrations(Student $student, ?int $semesterId = null): Collection
    {
        $query = $student->courseRegistrations()
            ->with([
                'courseOffering.curriculumUnit.unit',
                'courseOffering.lecturer',
                'courseOffering.classSessions.room',
                'semester'
            ]);

        if ($semesterId) {
            $query->where('semester_id', $semesterId);
        } else {
            // Get current semester registrations
            $currentSemester = Semester::where('is_active', true)->first();
            if ($currentSemester) {
                $query->where('semester_id', $currentSemester->id);
            }
        }

        return $query->get()->map(function ($registration) {
            return $this->formatRegistrationForStudent($registration);
        });
    }

    /**
     * Validate course registration
     */
    protected function validateRegistration(Student $student, CourseOffering $courseOffering): void
    {
        // Check if registration is open
        $currentSemester = Semester::where('is_active', true)->first();
        if (!$currentSemester || !$currentSemester->isRegistrationOpen()) {
            throw new BusinessLogicException('Registration is not currently open');
        }

        // Check if course is active
        if (!$courseOffering->is_active) {
            throw new BusinessLogicException('This course is not available for registration');
        }

        // Check enrollment capacity
        if (!$this->capacityService->hasAvailableCapacity($courseOffering)) {
            throw new BusinessLogicException('This course is full');
        }

        // Check prerequisites
        if (!$this->prerequisiteService->hasMetPrerequisites($student, $courseOffering)) {
            throw new BusinessLogicException('Prerequisites not met for this course');
        }

        // Check for schedule conflicts
        $conflicts = $this->conflictDetectionService->detectConflicts($student, $courseOffering);
        if (!$conflicts->isEmpty()) {
            throw new BusinessLogicException('Schedule conflict detected with existing registrations');
        }

        // Check if already registered
        $existingRegistration = $student->courseRegistrations()
            ->where('course_offering_id', $courseOffering->id)
            ->whereIn('registration_status', ['registered', 'pending'])
            ->exists();

        if ($existingRegistration) {
            throw new BusinessLogicException('Already registered for this course');
        }

        // Check credit hour limits
        $this->validateCreditHourLimits($student, $courseOffering);
    }

    /**
     * Validate credit hour limits
     */
    protected function validateCreditHourLimits(Student $student, CourseOffering $courseOffering): void
    {
        $currentSemester = Semester::where('is_active', true)->first();
        
        $currentCredits = $student->courseRegistrations()
            ->where('semester_id', $currentSemester->id)
            ->where('registration_status', 'registered')
            ->sum('credit_hours');

        $newTotalCredits = $currentCredits + $courseOffering->curriculumUnit->credit_hours;

        // Standard limits (can be made configurable)
        $maxCredits = 18; // Maximum credits per semester
        $minCredits = 12; // Minimum for full-time status

        if ($newTotalCredits > $maxCredits) {
            throw new BusinessLogicException("Registration would exceed maximum credit limit of {$maxCredits}");
        }
    }

    /**
     * Format course offering for registration display
     */
    protected function formatCourseOfferingForRegistration(CourseOffering $offering, Student $student): array
    {
        $enrolledCount = $offering->courseRegistrations->count();
        $availableSpots = max(0, $offering->max_enrollment - $enrolledCount);

        return [
            'id' => $offering->id,
            'unit' => [
                'code' => $offering->curriculumUnit->unit->code,
                'name' => $offering->curriculumUnit->unit->name,
                'description' => $offering->curriculumUnit->unit->description,
                'credit_hours' => $offering->curriculumUnit->credit_hours,
            ],
            'lecturer' => [
                'id' => $offering->lecturer?->id,
                'name' => $offering->lecturer?->full_name,
                'email' => $offering->lecturer?->email,
            ],
            'schedule' => $offering->classSessions->map(function ($session) {
                return [
                    'day_of_week' => $session->day_of_week,
                    'start_time' => $session->start_time,
                    'end_time' => $session->end_time,
                    'room' => [
                        'code' => $session->room?->code,
                        'name' => $session->room?->name,
                        'building' => $session->room?->building,
                    ],
                ];
            }),
            'enrollment' => [
                'current' => $enrolledCount,
                'maximum' => $offering->max_enrollment,
                'available' => $availableSpots,
                'waitlist_available' => $offering->allow_waitlist && $availableSpots === 0,
            ],
            'registration_eligibility' => [
                'can_register' => $this->canRegisterForCourse($student, $offering),
                'prerequisites_met' => $this->prerequisiteService->hasMetPrerequisites($student, $offering),
                'has_conflicts' => !$this->conflictDetectionService->detectConflicts($student, $offering)->isEmpty(),
                'capacity_available' => $availableSpots > 0,
            ],
        ];
    }

    /**
     * Format registration for student display
     */
    protected function formatRegistrationForStudent(CourseRegistration $registration): array
    {
        return [
            'id' => $registration->id,
            'status' => $registration->registration_status,
            'registration_date' => $registration->registration_date?->toDateString(),
            'drop_date' => $registration->drop_date?->toDateString(),
            'credit_hours' => $registration->credit_hours,
            'unit' => [
                'code' => $registration->courseOffering->curriculumUnit->unit->code,
                'name' => $registration->courseOffering->curriculumUnit->unit->name,
            ],
            'lecturer' => [
                'name' => $registration->courseOffering->lecturer?->full_name,
                'email' => $registration->courseOffering->lecturer?->email,
            ],
            'schedule' => $registration->courseOffering->classSessions->map(function ($session) {
                return [
                    'day_of_week' => $session->day_of_week,
                    'start_time' => $session->start_time,
                    'end_time' => $session->end_time,
                    'room' => $session->room?->code,
                ];
            }),
            'semester' => [
                'name' => $registration->semester->name,
                'code' => $registration->semester->code,
            ],
        ];
    }

    /**
     * Check if student can register for course
     */
    protected function canRegisterForCourse(Student $student, CourseOffering $offering): bool
    {
        try {
            $this->validateRegistration($student, $offering);
            return true;
        } catch (BusinessLogicException $e) {
            return false;
        }
    }
}
