<?php

declare(strict_types=1);

namespace App\Services\V1\Student;

use App\Models\Student;
use App\Models\CourseOffering;
use App\Models\Unit;
use App\Models\AcademicRecord;
use App\Models\UnitPrerequisite;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class PrerequisiteValidationService
{
    /**
     * Check if student has met all prerequisites for a course offering
     */
    public function hasMetPrerequisites(Student $student, CourseOffering $courseOffering): bool
    {
        $unit = $courseOffering->curriculumUnit->unit;
        $prerequisites = $this->getPrerequisites($unit);

        if ($prerequisites->isEmpty()) {
            return true;
        }

        return $this->validatePrerequisites($student, $prerequisites);
    }

    /**
     * Get detailed prerequisite validation results
     */
    public function getPrerequisiteValidation(Student $student, CourseOffering $courseOffering): array
    {
        $unit = $courseOffering->curriculumUnit->unit;
        $prerequisites = $this->getPrerequisites($unit);

        if ($prerequisites->isEmpty()) {
            return [
                'has_prerequisites' => false,
                'all_met' => true,
                'prerequisites' => [],
                'missing_prerequisites' => [],
            ];
        }

        $validationResults = [];
        $missingPrerequisites = [];

        foreach ($prerequisites as $prerequisite) {
            $result = $this->validateSinglePrerequisite($student, $prerequisite);
            $validationResults[] = $result;

            if (!$result['met']) {
                $missingPrerequisites[] = $result;
            }
        }

        return [
            'has_prerequisites' => true,
            'all_met' => empty($missingPrerequisites),
            'prerequisites' => $validationResults,
            'missing_prerequisites' => $missingPrerequisites,
        ];
    }

    /**
     * Get prerequisites for a unit
     */
    protected function getPrerequisites(Unit $unit): Collection
    {
        $cacheKey = "unit_prerequisites:{$unit->id}";

        return Cache::remember($cacheKey, 3600, function () use ($unit) {
            return UnitPrerequisite::where('unit_id', $unit->id)
                ->with(['prerequisiteUnit', 'prerequisiteGroup'])
                ->orderBy('prerequisite_type')
                ->orderBy('minimum_grade')
                ->get();
        });
    }

    /**
     * Validate all prerequisites for a student
     */
    protected function validatePrerequisites(Student $student, Collection $prerequisites): bool
    {
        $groupedPrerequisites = $prerequisites->groupBy('prerequisite_group_id');

        foreach ($groupedPrerequisites as $groupId => $groupPrerequisites) {
            if ($groupId === null) {
                // Individual prerequisites - all must be met
                foreach ($groupPrerequisites as $prerequisite) {
                    if (!$this->validateSinglePrerequisite($student, $prerequisite)['met']) {
                        return false;
                    }
                }
            } else {
                // Group prerequisites - check group logic
                if (!$this->validatePrerequisiteGroup($student, $groupPrerequisites)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate a single prerequisite
     */
    protected function validateSinglePrerequisite(Student $student, UnitPrerequisite $prerequisite): array
    {
        $prerequisiteUnit = $prerequisite->prerequisiteUnit;
        
        // Get student's academic record for this unit
        $academicRecord = $student->academicRecords()
            ->where('unit_id', $prerequisiteUnit->id)
            ->where('completion_status', 'completed')
            ->orderBy('completion_date', 'desc')
            ->first();

        $met = false;
        $gradeAchieved = null;
        $completionDate = null;

        if ($academicRecord) {
            $gradeAchieved = $academicRecord->final_letter_grade;
            $completionDate = $academicRecord->completion_date;
            
            // Check if grade meets minimum requirement
            $met = $this->gradeMetsMinimum($gradeAchieved, $prerequisite->minimum_grade);
        }

        return [
            'prerequisite_id' => $prerequisite->id,
            'prerequisite_type' => $prerequisite->prerequisite_type,
            'unit' => [
                'id' => $prerequisiteUnit->id,
                'code' => $prerequisiteUnit->code,
                'name' => $prerequisiteUnit->name,
            ],
            'minimum_grade_required' => $prerequisite->minimum_grade,
            'grade_achieved' => $gradeAchieved,
            'completion_date' => $completionDate?->toDateString(),
            'met' => $met,
            'is_concurrent_allowed' => $prerequisite->allow_concurrent,
        ];
    }

    /**
     * Validate prerequisite group (OR/AND logic)
     */
    protected function validatePrerequisiteGroup(Student $student, Collection $groupPrerequisites): bool
    {
        $group = $groupPrerequisites->first()->prerequisiteGroup;
        
        if (!$group) {
            return false;
        }

        $metCount = 0;
        $totalCount = $groupPrerequisites->count();

        foreach ($groupPrerequisites as $prerequisite) {
            if ($this->validateSinglePrerequisite($student, $prerequisite)['met']) {
                $metCount++;
            }
        }

        return match ($group->logic_type) {
            'AND' => $metCount === $totalCount,
            'OR' => $metCount >= 1,
            'MINIMUM' => $metCount >= ($group->minimum_required ?? 1),
            default => false,
        };
    }

    /**
     * Check if achieved grade meets minimum requirement
     */
    protected function gradeMetsMinimum(?string $achievedGrade, ?string $minimumGrade): bool
    {
        if (!$achievedGrade || !$minimumGrade) {
            return false;
        }

        $gradeValues = [
            'HD' => 4,
            'D' => 3,
            'C' => 2,
            'P' => 1,
            'N' => 0,
            'F' => 0,
        ];

        $achievedValue = $gradeValues[$achievedGrade] ?? 0;
        $minimumValue = $gradeValues[$minimumGrade] ?? 0;

        return $achievedValue >= $minimumValue;
    }

    /**
     * Get prerequisite tree for a unit (recursive)
     */
    public function getPrerequisiteTree(Unit $unit, int $depth = 0, int $maxDepth = 5): array
    {
        if ($depth > $maxDepth) {
            return [];
        }

        $prerequisites = $this->getPrerequisites($unit);
        
        if ($prerequisites->isEmpty()) {
            return [];
        }

        $tree = [];

        foreach ($prerequisites as $prerequisite) {
            $prerequisiteUnit = $prerequisite->prerequisiteUnit;
            
            $node = [
                'unit' => [
                    'id' => $prerequisiteUnit->id,
                    'code' => $prerequisiteUnit->code,
                    'name' => $prerequisiteUnit->name,
                ],
                'prerequisite_type' => $prerequisite->prerequisite_type,
                'minimum_grade' => $prerequisite->minimum_grade,
                'allow_concurrent' => $prerequisite->allow_concurrent,
                'depth' => $depth,
                'children' => $this->getPrerequisiteTree($prerequisiteUnit, $depth + 1, $maxDepth),
            ];

            $tree[] = $node;
        }

        return $tree;
    }

    /**
     * Get units that have this unit as a prerequisite (reverse lookup)
     */
    public function getUnitsRequiringAsPrerequisite(Unit $unit): Collection
    {
        return UnitPrerequisite::where('prerequisite_unit_id', $unit->id)
            ->with(['unit'])
            ->get()
            ->pluck('unit')
            ->unique('id');
    }

    /**
     * Check concurrent enrollment eligibility
     */
    public function canEnrollConcurrently(Student $student, CourseOffering $courseOffering, CourseOffering $prerequisiteCourseOffering): bool
    {
        $unit = $courseOffering->curriculumUnit->unit;
        $prerequisiteUnit = $prerequisiteCourseOffering->curriculumUnit->unit;

        $prerequisite = UnitPrerequisite::where('unit_id', $unit->id)
            ->where('prerequisite_unit_id', $prerequisiteUnit->id)
            ->first();

        if (!$prerequisite) {
            return false;
        }

        return $prerequisite->allow_concurrent;
    }

    /**
     * Get recommended study sequence based on prerequisites
     */
    public function getRecommendedSequence(Student $student, Collection $units): array
    {
        $sequence = [];
        $completed = $this->getCompletedUnits($student);
        $remaining = $units->reject(function ($unit) use ($completed) {
            return $completed->contains('id', $unit->id);
        });

        $level = 1;
        while ($remaining->isNotEmpty() && $level <= 10) {
            $availableThisLevel = $remaining->filter(function ($unit) use ($student, $completed) {
                $prerequisites = $this->getPrerequisites($unit);
                
                if ($prerequisites->isEmpty()) {
                    return true;
                }

                // Check if all prerequisites are completed
                foreach ($prerequisites as $prerequisite) {
                    $prerequisiteUnit = $prerequisite->prerequisiteUnit;
                    if (!$completed->contains('id', $prerequisiteUnit->id)) {
                        return false;
                    }
                }

                return true;
            });

            if ($availableThisLevel->isEmpty()) {
                break; // Circular dependency or missing prerequisites
            }

            $sequence[] = [
                'level' => $level,
                'semester' => ceil($level / 2),
                'units' => $availableThisLevel->values()->toArray(),
            ];

            // Add these units to completed for next iteration
            $completed = $completed->merge($availableThisLevel);
            $remaining = $remaining->reject(function ($unit) use ($availableThisLevel) {
                return $availableThisLevel->contains('id', $unit->id);
            });

            $level++;
        }

        // Add any remaining units that couldn't be sequenced
        if ($remaining->isNotEmpty()) {
            $sequence[] = [
                'level' => $level,
                'semester' => ceil($level / 2),
                'units' => $remaining->values()->toArray(),
                'note' => 'Units with unresolved prerequisites',
            ];
        }

        return $sequence;
    }

    /**
     * Get completed units for a student
     */
    protected function getCompletedUnits(Student $student): Collection
    {
        return $student->academicRecords()
            ->where('completion_status', 'completed')
            ->with('unit')
            ->get()
            ->pluck('unit')
            ->unique('id');
    }

    /**
     * Clear prerequisite cache for a unit
     */
    public function clearPrerequisiteCache(Unit $unit): void
    {
        $cacheKey = "unit_prerequisites:{$unit->id}";
        Cache::forget($cacheKey);
    }
}
